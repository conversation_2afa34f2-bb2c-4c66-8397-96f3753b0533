// Initialize global constants first
require('../../app/utils/global-constants.utils');

describe('Project Tracker Completion Fix', () => {
  describe('Fixed completion calculation logic', () => {
    it('should sum all completion durations without redundant ID checks', () => {
      // Mock data that simulates the fixed logic
      const mockProjectTrackerData = [
        {
          reportList: [
            {
              _id: 'report1',
              reportList: [
                { _id: 'instance1', completedDuration: 50 },
                { _id: 'instance2', completedDuration: 30 },
                { _id: 'instance3', completedDuration: 20 }
              ]
            }
          ]
        }
      ];

      const mockScopeData = [
        {
          reports: [
            {
              _id: 'report1',
              totalDurationReport: 100
            }
          ]
        }
      ];

      const mockTotalDurationsOfAllReports = 100;

      // Simulate the FIXED logic (sum all instances, not just matching IDs)
      let totalCompletions = 0;
      
      const result = mockScopeData.map(scope => ({
        ...scope,
        reports: scope.reports.map(report => {
          let totalCompletedReportDuration = 0;
          mockProjectTrackerData.forEach(projectTracker => {
            // Look for the report in the reportList
            const foundReport = projectTracker.reportList.find(
              reportItem => reportItem._id.toString() === report._id.toString()
            );
            
            if (foundReport && foundReport.reportList) {
              // Sum up completed durations from all instances of this report
              foundReport.reportList.forEach(reportInstance => {
                // FIXED: Since we already found the correct report, sum all instances
                totalCompletedReportDuration += reportInstance.completedDuration || 0;
              });
            }
          });

          totalCompletions +=
            (totalCompletedReportDuration / report.totalDurationReport) *
              (report.totalDurationReport / mockTotalDurationsOfAllReports) || 0;

          return {
            ...report,
            totalCompletedReportDuration,
            reportCompletion: (totalCompletedReportDuration / report.totalDurationReport) * 100,
          };
        }),
      }));

      // Test that all completion durations are summed correctly
      expect(result).toBeDefined();
      expect(result[0].reports[0].totalCompletedReportDuration).toBe(100); // 50 + 30 + 20
      expect(result[0].reports[0].reportCompletion).toBe(100); // 100/100 * 100 = 100%
      expect(totalCompletions).toBe(1); // 100% completion
    });

    it('should handle the old buggy logic for comparison', () => {
      // Mock data that simulates the OLD BUGGY logic
      const mockProjectTrackerData = [
        {
          reportList: [
            {
              _id: 'report1',
              reportList: [
                { _id: 'report1', completedDuration: 50 }, // Only this would be counted
                { _id: 'instance2', completedDuration: 30 }, // This would be ignored
                { _id: 'instance3', completedDuration: 20 }  // This would be ignored
              ]
            }
          ]
        }
      ];

      const mockScopeData = [
        {
          reports: [
            {
              _id: 'report1',
              totalDurationReport: 100
            }
          ]
        }
      ];

      const mockTotalDurationsOfAllReports = 100;

      // Simulate the OLD BUGGY logic (only sum instances with matching report ID)
      let totalCompletions = 0;
      
      const result = mockScopeData.map(scope => ({
        ...scope,
        reports: scope.reports.map(report => {
          let totalCompletedReportDuration = 0;
          mockProjectTrackerData.forEach(projectTracker => {
            const foundReport = projectTracker.reportList.find(
              reportItem => reportItem._id.toString() === report._id.toString()
            );
            
            if (foundReport && foundReport.reportList) {
              foundReport.reportList.forEach(reportInstance => {
                // OLD BUGGY LOGIC: redundant ID check
                if (reportInstance._id.toString() === report._id.toString()) {
                  totalCompletedReportDuration += reportInstance.completedDuration || 0;
                }
              });
            }
          });

          totalCompletions +=
            (totalCompletedReportDuration / report.totalDurationReport) *
              (report.totalDurationReport / mockTotalDurationsOfAllReports) || 0;

          return {
            ...report,
            totalCompletedReportDuration,
            reportCompletion: (totalCompletedReportDuration / report.totalDurationReport) * 100,
          };
        }),
      }));

      // Test that only matching IDs are summed (demonstrating the bug)
      expect(result).toBeDefined();
      expect(result[0].reports[0].totalCompletedReportDuration).toBe(50); // Only the matching ID
      expect(result[0].reports[0].reportCompletion).toBe(50); // 50/100 * 100 = 50%
      expect(totalCompletions).toBe(0.5); // 50% completion
    });
  });
});
