// Initialize global constants first
require('../../app/utils/global-constants.utils');

describe('Project Tracker Completion Calculation', () => {
  describe('projectTrackerData completion calculation fix', () => {
    it('should handle missing reportIndex gracefully without throwing errors', () => {
      // Mock data that simulates the scenario where reportIndex is -1
      const mockProjectTrackerData = [
        {
          reportList: [
            {
              _id: 'report1',
              reportList: [
                {
                  _id: 'report1',
                  completedDuration: 100
                }
              ]
            }
          ]
        }
      ];

      const mockScopeData = [
        {
          reports: [
            {
              _id: 'report2', // This ID doesn't exist in projectTrackerData, causing reportIndex = -1
              totalDurationReport: 200
            }
          ]
        }
      ];

      const mockTotalDurationsOfAllReports = 200;

      // Simulate the fixed logic
      let totalCompletions = 0;
      
      const result = mockScopeData.map(scope => ({
        ...scope,
        reports: scope.reports.map(report => {
          let totalCompletedReportDuration = 0;
          mockProjectTrackerData.forEach(projectTracker => {
            const reportIndex = projectTracker.reportList.findIndex(
              reportList => reportList._id.toString() === report._id.toString()
            );
            // The fix: Check if reportIndex is valid before accessing the array
            if (reportIndex !== -1 && projectTracker.reportList[reportIndex].reportList) {
              projectTracker.reportList[reportIndex].reportList.forEach(reportList => {
                if (reportList._id.toString() === report._id.toString()) {
                  totalCompletedReportDuration += reportList.completedDuration;
                }
              });
            }
          });

          totalCompletions +=
            (totalCompletedReportDuration / report.totalDurationReport) *
              (report.totalDurationReport / mockTotalDurationsOfAllReports) || 0;

          return {
            ...report,
            totalCompletedReportDuration,
            reportCompletion: (totalCompletedReportDuration / report.totalDurationReport) * 100,
          };
        }),
      }));

      // Test that the function doesn't throw an error and handles the case gracefully
      expect(result).toBeDefined();
      expect(result[0].reports[0].totalCompletedReportDuration).toBe(0);
      expect(result[0].reports[0].reportCompletion).toBe(0);
      expect(totalCompletions).toBe(0);
    });

    it('should calculate completion correctly when reports exist', () => {
      // Mock data that simulates successful completion calculation
      const mockProjectTrackerData = [
        {
          reportList: [
            {
              _id: 'report1',
              reportList: [
                {
                  _id: 'report1',
                  completedDuration: 100
                }
              ]
            }
          ]
        }
      ];

      const mockScopeData = [
        {
          reports: [
            {
              _id: 'report1', // This ID exists in projectTrackerData
              totalDurationReport: 100
            }
          ]
        }
      ];

      const mockTotalDurationsOfAllReports = 100;

      // Simulate the fixed logic
      let totalCompletions = 0;
      
      const result = mockScopeData.map(scope => ({
        ...scope,
        reports: scope.reports.map(report => {
          let totalCompletedReportDuration = 0;
          mockProjectTrackerData.forEach(projectTracker => {
            const reportIndex = projectTracker.reportList.findIndex(
              reportList => reportList._id.toString() === report._id.toString()
            );
            // The fix: Check if reportIndex is valid before accessing the array
            if (reportIndex !== -1 && projectTracker.reportList[reportIndex].reportList) {
              projectTracker.reportList[reportIndex].reportList.forEach(reportList => {
                if (reportList._id.toString() === report._id.toString()) {
                  totalCompletedReportDuration += reportList.completedDuration;
                }
              });
            }
          });

          totalCompletions +=
            (totalCompletedReportDuration / report.totalDurationReport) *
              (report.totalDurationReport / mockTotalDurationsOfAllReports) || 0;

          return {
            ...report,
            totalCompletedReportDuration,
            reportCompletion: (totalCompletedReportDuration / report.totalDurationReport) * 100,
          };
        }),
      }));

      // Test that completion is calculated correctly when report exists
      expect(result).toBeDefined();
      expect(result[0].reports[0].totalCompletedReportDuration).toBe(100);
      expect(result[0].reports[0].reportCompletion).toBe(100);
      expect(totalCompletions).toBe(1); // 100% completion
    });
  });
});
